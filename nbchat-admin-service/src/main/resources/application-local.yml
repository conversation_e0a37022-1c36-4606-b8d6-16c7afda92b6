spring:
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8  # 时区
    serialization: # 不返回时间戳
      write-dates-as-timestamps: false
  redis:
    host: ************** #************** #127.0.0.1
    port: 6389 #16379
    password: Redis235 #Redis235 #Mtj1qJSwLF
    timeout: 1000ms # 连接超时时长（毫秒）
    jedis:
      pool:
        max-wait: -1
        max-active: 1000
        max-idle: 10
        min-idle: 5
    database: 5
  datasource:
    dynamic:
      primary: master
      druid:
        initial-size: 3
        max-active: 10
        min-idle: 2
        max-wait: -1
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 30000
        time-between-eviction-runs-millis: 3000
        max-pool-prepared-statement-per-connection-size: 20
        validation-query: select 1
        validation-query-timeout: -1
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        pool-prepared-statements: true
        filters: stat,wall
        share-prepared-statements: true
        wall:
          multi-statement-allow: true

      datasource:
        master:
          username: dml_user
          password: QfNWG4di_dml_dev
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************************************************************************************************************************
#          username: nbchat
#          password: nbchat#2024!23
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: *************************************************************************************************************************************************************************************************************************

# mybatis
mybatis:
  mapperLocations: classpath:mapper/admin/*.xml
  type-aliases-package: com.tydic.nbchat.admin.mapper.po
# 分页组件
pagehelper:
  helperDialect: mysql
  params: count=countSql

nacos:
  config:
    server-addr: 127.0.0.1:8848

# dubbo 服务配置
dubbo:
  application:
    name: nbchat-admin
  registry:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    # 可以通过url ?namespace=nicc-env-dev&group=NICC的形式配置，也可以通过这种parameters配置
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  provider:
    threads: 300
    threadpool: cached
    loadbalance: roundrobin
    version: 1.0
    group: NICC
  protocol:
    name: dubbo
    port: -1
  # 配置中心和元数据
  config-center:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  metadata-report:
    username: nacos
    password: nacos
    address: nacos://${nacos.config.server-addr}
    parameters[namespace]: ${nacos.config.namespace:public}
    parameters[group]: ${nacos.config.group:DEFAULT_GROUP}
  consumer:
    check: false
    version: 1.0
    group: NICC

nicc-dc-config:
  dubbo-provider:
    version: 1.0
    group: NICC

# 数据中心相关组件配置
nicc-plugin:
  redis:
    enable: true # 是否开启redisHelper，开启后必须配置spring.redis，同时直接用RedisHelper
  oss:
    enable: false
  minio:
    enable: true
    endpoint: http://**************:9090
    port: 9090
    accessKey: 8P1oTV9MvSTbxJwi
    secretKey: 1HcbEcNTzr7EjPEKsZps4gF8ChyfiHIC
    bucketName: nbchat-dev
    access-url: https://chatfiles-test.tydiczt.com/files
  rest:
    enable: true # 开启restTemplate 组件
    connect-timeout: 5000
    read-timeout: 5000
    print-log: true
  ftp:
    enable: false
  kkmq:
    mq-type: rocketmq
    log-level: warn
    name-server: **************:9878 # rocketmq服务地址
    producer:
      group: nbchat_admin # 自定义的组名称

upload-file-manage:
  tmp-path: /tmp
  file-id-mask: true
  suffix: "*"
  upload-type: minio # 上传类型 local本地,oss,ftp,sftp
  local-path: /middleware/nginx/html/files/
  access-url-prefix: https://chat-test.tydiczt.com/files/ # 文件访问前缀,配nginx访问文件的前缀或者oss访问前缀
  allow-referers: # 防盗链 - 列表方式配置,不配置全部放行
  white-list: # 白名单 - 列表方式配置,不配置全部放行
  black-list: # 黑名单 - 列表方式配置,不配置全部放行
  image-compress:
    enable: true
    over-size: 1024
    dest-size: 1000
  file-check-enable: false

nbchat-admin:
  config:
    tenant-invite-expire-time: 86400
    tenant-invite-url: https://chat-test.tydiczt.com/chat/?inviteKey={inviteKey}#/intelligentTraining
    http-proxy:
      enable: false
      type: http
      port: 3128
      username: nbchat
      password: 010.tydic
      host: *************
    qichacha:
      url: https://api.qichacha.com/NameSearch/GetList?key=KEY&searchName=SEARCHNAME
      key: da40963b60bb443a9b6d2e832c28f221
      secret-key: 24BCD6E4952D676842D9992A10E6DCA9
    report-timer: true
    baidu:
      ocpc-token:
    qihu:
      app-key: hwu346kps8hy99q7bhpf # 360点睛平台App-Key
      app-secret: # 360点睛平台App-Secret
      base-url: https://convert.dop.360.cn # 360点睛平台基础URL
    default-password-prefix: Unicom#
    default-robot: fastchat
  knowledge-config:
    share-key-length: 4
    share-url-prefix: https://chat-test.tydiczt.com/chat/#/knowledgeShare?shareKey=
  s-link:
    video-share-prefix: https://chat-test.tydiczt.com/api/videoShare/?
    s-link-prefix: https://chat-test.tydiczt.com/api/s/
logging:
  config: classpath:logback-spring.xml
  level:
    com.tydic.nbchat.admin.mapper: debug
