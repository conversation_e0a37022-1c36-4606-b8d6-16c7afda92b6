{"info": {"name": "360 点睛 Web 转化数据 API Collection", "_postman_id": "e9d3e67b-c60d-469a-9205-6b9b6d8b6e40", "description": "根据 360 点睛-网页转化数据 API 对接文档自动生成，可直接导入 Postman 使用。", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "PC 搜索推广 (ocpc_ps_convert)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 自动生成 request_time", "if (!pm.variables.get(\"timestamp\") || pm.variables.get(\"timestamp\") === \"\") {", "    pm.variables.set(\"timestamp\", Math.floor(Date.now() / 1000));", "}", "", "const secret = pm.environment.get(\"Secret\") || pm.variables.get(\"Secret\");", "const body = pm.request.body ? pm.request.body.raw : '';", "const sign = CryptoJS.MD5(secret + body).toString();", "pm.request.headers.upsert({ key: \"App-Sign\", value: sign });"]}}], "request": {"method": "POST", "header": [{"key": "App-Key", "value": "{{Key}}"}, {"key": "Content-Type", "value": "application/json;charset=utf-8"}], "url": {"raw": "{{BaseURL}}/uploadWebConvert", "host": ["{{BaseURL}}"], "path": ["uploadWebConvert"]}, "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"request_time\": \"{{timestamp}}\",\n    \"data_industry\": \"ocpc_ps_convert\",\n    \"data_detail\": {\n      \"qhclickid\": \"{{qhclickid}}\",\n      \"trans_id\": \"{{trans_id}}\",\n      \"event\": \"SUBMIT\",\n      \"event_time\": \"{{timestamp}}\"\n    }\n  }\n}"}, "description": "示例请求：PC 搜索推广 (ocpc_ps_convert) - ocpc_ps_convert"}}, {"name": "移动搜索 (ocpc_ms_convert)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 自动生成 request_time", "if (!pm.variables.get(\"timestamp\") || pm.variables.get(\"timestamp\") === \"\") {", "    pm.variables.set(\"timestamp\", Math.floor(Date.now() / 1000));", "}", "", "const secret = pm.environment.get(\"Secret\") || pm.variables.get(\"Secret\");", "const body = pm.request.body ? pm.request.body.raw : '';", "const sign = CryptoJS.MD5(secret + body).toString();", "pm.request.headers.upsert({ key: \"App-Sign\", value: sign });"]}}], "request": {"method": "POST", "header": [{"key": "App-Key", "value": "{{Key}}"}, {"key": "Content-Type", "value": "application/json;charset=utf-8"}], "url": {"raw": "{{BaseURL}}/uploadWebConvert", "host": ["{{BaseURL}}"], "path": ["uploadWebConvert"]}, "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"request_time\": \"{{timestamp}}\",\n    \"data_industry\": \"ocpc_ms_convert\",\n    \"data_detail\": {\n      \"qhclickid\": \"{{qhclickid}}\",\n      \"trans_id\": \"{{trans_id}}\",\n      \"event\": \"SUBMIT\",\n      \"event_time\": \"{{timestamp}}\"\n    }\n  }\n}"}, "description": "示例请求：移动搜索 (ocpc_ms_convert) - ocpc_ms_convert"}}, {"name": "展示广告 (ocpc_zs_convert)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 自动生成 request_time", "if (!pm.variables.get(\"timestamp\") || pm.variables.get(\"timestamp\") === \"\") {", "    pm.variables.set(\"timestamp\", Math.floor(Date.now() / 1000));", "}", "", "const secret = pm.environment.get(\"Secret\") || pm.variables.get(\"Secret\");", "const body = pm.request.body ? pm.request.body.raw : '';", "const sign = CryptoJS.MD5(secret + body).toString();", "pm.request.headers.upsert({ key: \"App-Sign\", value: sign });"]}}], "request": {"method": "POST", "header": [{"key": "App-Key", "value": "{{Key}}"}, {"key": "Content-Type", "value": "application/json;charset=utf-8"}], "url": {"raw": "{{BaseURL}}/uploadWebConvert", "host": ["{{BaseURL}}"], "path": ["uploadWebConvert"]}, "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"request_time\": \"{{timestamp}}\",\n    \"data_industry\": \"ocpc_zs_convert\",\n    \"data_detail\": {\n      \"qhclickid\": \"{{qhclickid}}\",\n      \"jzqs\": \"{{jzqs}}\",\n      \"trans_id\": \"{{trans_id}}\",\n      \"event\": \"ORDER\",\n      \"event_time\": \"{{timestamp}}\"\n    }\n  }\n}"}, "description": "示例请求：展示广告 (ocpc_zs_convert) - ocpc_zs_convert"}}, {"name": "移动推广 (ocpc_web_convert)", "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 自动生成 request_time", "if (!pm.variables.get(\"timestamp\") || pm.variables.get(\"timestamp\") === \"\") {", "    pm.variables.set(\"timestamp\", Math.floor(Date.now() / 1000));", "}", "", "const secret = pm.environment.get(\"Secret\") || pm.variables.get(\"Secret\");", "const body = pm.request.body ? pm.request.body.raw : '';", "const sign = CryptoJS.MD5(secret + body).toString();", "pm.request.headers.upsert({ key: \"App-Sign\", value: sign });"]}}], "request": {"method": "POST", "header": [{"key": "App-Key", "value": "{{Key}}"}, {"key": "Content-Type", "value": "application/json;charset=utf-8"}], "url": {"raw": "{{BaseURL}}/uploadWebConvert", "host": ["{{BaseURL}}"], "path": ["uploadWebConvert"]}, "body": {"mode": "raw", "raw": "{\n  \"data\": {\n    \"request_time\": \"{{timestamp}}\",\n    \"data_industry\": \"ocpc_web_convert\",\n    \"data_detail\": {\n      \"impression_id\": \"{{impression_id}}\",\n      \"trans_id\": \"{{trans_id}}\",\n      \"event\": \"SUBMIT\",\n      \"event_time\": \"{{timestamp}}\"\n    }\n  }\n}"}, "description": "示例请求：移动推广 (ocpc_web_convert) - ocpc_web_convert"}}], "variable": [{"key": "BaseURL", "value": "https://convert.dop.360.cn"}, {"key": "Key", "value": ""}, {"key": "Secret", "value": ""}, {"key": "timestamp", "value": ""}, {"key": "qhc<PERSON>id", "value": ""}, {"key": "impression_id", "value": ""}, {"key": "jzqs", "value": ""}, {"key": "trans_id", "value": ""}]}