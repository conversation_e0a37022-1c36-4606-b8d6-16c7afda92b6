# 360点睛广告平台API对接实现

## 概述

本项目实现了360点睛广告平台的转化数据回传API对接，参考现有的百度广告主回传数据接口的设计模式，提供了完整的360平台集成解决方案。

## 项目结构

### API层 (nbchat-admin-api)
```
com.tydic.nbchat.admin.api.qihu/
├── QihuApiCoordinationService.java          # 360服务接口
└── bo/
    ├── QihuApiCoordinationReqBO.java        # 360请求参数BO
    ├── QihuConversionDataBO.java            # 360转化数据BO
    └── QihuDataDetailBO.java                # 360数据详情BO
```

### 核心实现层 (nbchat-admin-core)
```
com.tydic.nbchat.admin.core/
├── controller/
│   └── QihuApiCoordinationController.java   # 360控制器
├── service/impl/
│   └── QihuApiCoordinationServiceImpl.java  # 360服务实现
├── config/properties/
│   └── QihuConfigProperties.java            # 360配置属性
└── utils/
    ├── QihuSendConvertDataUtil.java         # 360数据发送工具
    └── QihuConversionDataBuilder.java       # 360数据构建工具
```

### 测试 (nbchat-admin-core/test)
```
com.tydic.nbchat.admin.core/
└── QihuApiTest.java                         # 360接口测试用例
```

## 功能特性

### 1. 支持的广告类型
- **PC搜索推广** (ocpc_ps_convert)
- **移动搜索** (ocpc_ms_convert)
- **展示广告** (ocpc_zs_convert)
- **移动推广** (ocpc_web_convert)

### 2. 核心功能
- ✅ MD5签名认证
- ✅ 自动重试机制 (3次)
- ✅ 完整的参数校验
- ✅ 详细的日志记录
- ✅ 错误处理和异常捕获
- ✅ 配置化管理

### 3. 设计模式
- 参考百度API实现，保持代码风格一致
- 使用Builder模式简化数据构建
- 采用工厂模式支持多种广告类型
- 遵循Spring Boot最佳实践

## 配置说明

### application-local.yml
```yaml
nbchat-admin:
  config:
    qihu:
      app-key: your_360_app_key        # 360点睛平台App-Key
      app-secret: your_360_app_secret  # 360点睛平台App-Secret
      base-url: https://convert.dop.360.cn  # 360点睛平台基础URL
```

## API接口

### 接口地址
```
POST /admin/qihu/dataConversionCallback
```

### 请求示例
```json
{
  "data": {
    "requestTime": 1640995200,
    "dataIndustry": "ocpc_ps_convert",
    "dataDetail": {
      "qhclickid": "click_id_123",
      "transId": "trans_123",
      "event": "SUBMIT",
      "eventTime": 1640995200
    }
  }
}
```

## 使用示例

### 1. 直接调用服务
```java
@Autowired
private QihuApiCoordinationService qihuApiCoordinationService;

// 构建请求数据
QihuApiCoordinationReqBO reqBo = new QihuApiCoordinationReqBO();
QihuConversionDataBO conversionData = QihuConversionDataBuilder.buildPcSearchConvert(
    "your_click_id", "your_trans_id", "SUBMIT"
);
reqBo.setData(conversionData);

// 调用接口
Rsp result = qihuApiCoordinationService.dataConversionCallback(reqBo);
```

### 2. 使用构建工具
```java
// PC搜索推广
QihuConversionDataBO pcData = QihuConversionDataBuilder.buildPcSearchConvert(
    "click_id", "trans_id", "SUBMIT"
);

// 移动搜索
QihuConversionDataBO mobileData = QihuConversionDataBuilder.buildMobileSearchConvert(
    "click_id", "trans_id", "SUBMIT"
);

// 展示广告
QihuConversionDataBO displayData = QihuConversionDataBuilder.buildDisplayAdConvert(
    "click_id", "jzqs", "trans_id", "ORDER"
);

// 移动推广
QihuConversionDataBO promotionData = QihuConversionDataBuilder.buildMobilePromotionConvert(
    "impression_id", "trans_id", "SUBMIT"
);
```

## 技术实现细节

### 1. 签名机制
- 使用MD5算法
- 签名字符串: `app_secret + request_body`
- 签名结果放在请求头 `App-Sign` 中

### 2. 重试策略
- 最大重试次数: 3次
- 重试条件: HTTP状态码非200或响应状态码为500
- 重试间隔: 立即重试

### 3. 错误处理
- 参数校验失败: 返回错误信息
- 网络异常: 自动重试
- 服务端错误: 记录日志并返回失败

### 4. 日志记录
- 请求参数日志
- 响应结果日志
- 错误异常日志
- 重试次数日志

## 测试

### 运行单元测试
```bash
mvn test -Dtest=QihuApiTest
```

### 测试覆盖
- PC搜索推广转化测试
- 移动搜索转化测试
- 展示广告转化测试
- 移动推广转化测试

## 部署说明

### 1. 配置环境变量
```bash
export QIHU_APP_KEY=your_app_key
export QIHU_APP_SECRET=your_app_secret
```

### 2. 更新配置文件
确保 `application-local.yml` 中的360配置正确

### 3. 重启应用
```bash
mvn spring-boot:run
```

## 监控和维护

### 1. 日志监控
- 关注转化数据回传成功率
- 监控API响应时间
- 跟踪错误日志和异常

### 2. 性能优化
- 连接池配置优化
- 超时时间调整
- 重试策略优化

### 3. 安全考虑
- App-Secret安全存储
- 请求签名验证
- 防重放攻击

## 与百度API的对比

| 特性 | 百度API | 360API |
|------|---------|--------|
| 认证方式 | Token | App-Key + App-Secret |
| 签名算法 | 无 | MD5 |
| 数据格式 | ConversionTypes数组 | 单个data对象 |
| 重试机制 | ✅ | ✅ |
| 错误处理 | ✅ | ✅ |

## 后续扩展

### 1. 支持更多广告平台
- 腾讯广告
- 字节跳动广告
- 阿里妈妈

### 2. 功能增强
- 批量数据回传
- 异步处理
- 数据缓存

### 3. 监控告警
- 成功率监控
- 延迟监控
- 错误告警

## 技术支持

如有问题，请联系：
- 开发团队
- 360点睛平台技术支持
- 查阅官方API文档

## 版本历史

- v1.0.0: 初始版本，支持基础转化数据回传
- 计划v1.1.0: 支持批量回传和异步处理
