package com.tydic.nbchat.admin.core.utils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.tydic.nbchat.admin.api.bo.qihu.QihuConversionDataBO;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class QihuSendConvertDataUtil {
    
    private static final String QIHU_UPLOAD_URL = "/uploadWebConvert";
    private static final Integer RETRY_TIMES = 3;
    private static final Logger log = LoggerFactory.getLogger(QihuSendConvertDataUtil.class);

    /**
     * 360数据回传接口
     *
     * @param baseUrl 360基础URL
     * @param appKey 360应用Key
     * @param appSecret 360应用Secret
     * @param conversionData 回传转化数据
     * @return 返回true代表成功 false代表失败
     */
    public static Boolean sendConvertData(String baseUrl, String appKey, String appSecret, QihuConversionDataBO conversionData) {
        
        // 构建请求数据
        JsonObject requestData = new JsonObject();
        requestData.add("data", new Gson().toJsonTree(conversionData));
        
        String requestBody = requestData.toString();
        log.info("360转化数据回传请求: {}", requestBody);
        
        // 生成签名
        String sign = generateSign(appSecret, requestBody);
        
        // 发送请求
        return sendWithRetry(baseUrl + QIHU_UPLOAD_URL, appKey, sign, requestBody);
    }

    /**
     * 生成MD5签名
     * 
     * @param secret 密钥
     * @param body 请求体
     * @return MD5签名
     */
    private static String generateSign(String secret, String body) {
        try {
            String signStr = secret + body;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.getBytes(Charset.forName("UTF-8")));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("生成MD5签名失败", e);
            return "";
        }
    }

    /**
     * 带重试的发送请求
     * 
     * @param url 请求URL
     * @param appKey 应用Key
     * @param sign 签名
     * @param requestBody 请求体
     * @return 是否成功
     */
    private static boolean sendWithRetry(String url, String appKey, String sign, String requestBody) {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        
        // 设置请求头
        post.setHeader("Content-Type", "application/json;charset=utf-8");
        post.setHeader("App-Key", appKey);
        post.setHeader("App-Sign", sign);
        
        StringEntity entity = new StringEntity(requestBody, Charset.forName("UTF-8"));
        entity.setContentEncoding("UTF-8");
        post.setEntity(entity);
        
        // 添加失败重试
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                HttpResponse response = client.execute(post);
                int code = response.getStatusLine().getStatusCode();
                
                if (code == HttpStatus.SC_OK) {
                    String res = EntityUtils.toString(response.getEntity());
                    log.info("360转化数据回传响应 (重试次数: {}): {}", i, res);
                    
                    // 解析响应结果
                    JsonObject returnData = new JsonParser().parse(res).getAsJsonObject();
                    
                    // 检查响应状态
                    if (returnData.has("status")) {
                        int status = returnData.get("status").getAsInt();
                        // status为0代表成功
                        if (status == 0) {
                            return true;
                        }
                        // 如果是服务端错误，可以重试
                        if (status != 500) {
                            return false;
                        }
                    }
                }
            } catch (IOException e) {
                log.error("360转化数据回传请求失败 (重试次数: {})", i, e);
            }
        }
        return false;
    }
}
