package com.tydic.nbchat.admin.core;

import com.tydic.nbchat.admin.api.bo.qihu.QihuApiCoordinationReqBO;
import com.tydic.nbchat.admin.api.bo.qihu.QihuConversionDataBO;
import com.tydic.nbchat.admin.api.bo.qihu.QihuDataDetailBO;
import com.tydic.nbchat.admin.core.utils.QihuSendConvertDataUtil;
import com.tydic.nbchat.admin.core.utils.QihuConversionDataBuilder;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * 360广告接口测试
 */
@Slf4j
public class QihuApiTest {

    @Test
    public void testPcSearchConvert() {
        // 测试PC搜索推广转化数据回传
        QihuConversionDataBO conversionData = QihuConversionDataBuilder.buildPcSearchConvert(
            "test_qhclickid_123", "test_trans_id_123", "SUBMIT"
        );
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("PC搜索推广转化数据回传结果: {}", result);
    }

    @Test
    public void testMobileSearchConvert() {
        // 测试移动搜索转化数据回传
        QihuConversionDataBO conversionData = QihuConversionDataBuilder.buildMobileSearchConvert(
            "test_qhclickid_456", "test_trans_id_456", "SUBMIT"
        );
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("移动搜索转化数据回传结果: {}", result);
    }

    @Test
    public void testDisplayAdConvert() {
        // 测试展示广告转化数据回传
        QihuConversionDataBO conversionData = QihuConversionDataBuilder.buildDisplayAdConvert(
            "test_qhclickid_789", "test_jzqs_789", "test_trans_id_789", "ORDER"
        );
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("展示广告转化数据回传结果: {}", result);
    }

    @Test
    public void testMobilePromotionConvert() {
        // 测试移动推广转化数据回传
        QihuConversionDataBO conversionData = QihuConversionDataBuilder.buildMobilePromotionConvert(
            "test_impression_id_101", "test_trans_id_101", "SUBMIT"
        );
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("移动推广转化数据回传结果: {}", result);
    }

    @Test
    public void testCustomConvert() {
        // 测试自定义转化数据回传
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid("custom_click_id");
        dataDetail.setTransId("custom_trans_id");
        dataDetail.setEvent("SUBMIT");
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        QihuConversionDataBO conversionData = QihuConversionDataBuilder.buildCustomConvert(
            "ocpc_ps_convert", dataDetail
        );
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("自定义转化数据回传结果: {}", result);
    }
}
