package com.tydic.nbchat.admin.core;

import com.tydic.nbchat.admin.api.bo.qihu.QihuApiCoordinationReqBO;
import com.tydic.nbchat.admin.api.bo.qihu.QihuConversionDataBO;
import com.tydic.nbchat.admin.api.bo.qihu.QihuDataDetailBO;
import com.tydic.nbchat.admin.core.utils.QihuSendConvertDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * 360广告接口测试
 */
@Slf4j
public class QihuApiTest {

    @Test
    public void testPcSearchConvert() {
        // 测试PC搜索推广转化数据回传
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_ps_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid("test_qhclickid_123");
        dataDetail.setTransId("test_trans_id_123");
        dataDetail.setEvent("SUBMIT");
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("PC搜索推广转化数据回传结果: {}", result);
    }

    @Test
    public void testMobileSearchConvert() {
        // 测试移动搜索转化数据回传
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_ms_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid("test_qhclickid_456");
        dataDetail.setTransId("test_trans_id_456");
        dataDetail.setEvent("SUBMIT");
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("移动搜索转化数据回传结果: {}", result);
    }

    @Test
    public void testDisplayAdConvert() {
        // 测试展示广告转化数据回传
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_zs_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setQhclickid("test_qhclickid_789");
        dataDetail.setJzqs("test_jzqs_789");
        dataDetail.setTransId("test_trans_id_789");
        dataDetail.setEvent("ORDER");
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("展示广告转化数据回传结果: {}", result);
    }

    @Test
    public void testMobilePromotionConvert() {
        // 测试移动推广转化数据回传
        QihuConversionDataBO conversionData = new QihuConversionDataBO();
        conversionData.setRequestTime(System.currentTimeMillis() / 1000);
        conversionData.setDataIndustry("ocpc_web_convert");
        
        QihuDataDetailBO dataDetail = new QihuDataDetailBO();
        dataDetail.setImpressionId("test_impression_id_101");
        dataDetail.setTransId("test_trans_id_101");
        dataDetail.setEvent("SUBMIT");
        dataDetail.setEventTime(System.currentTimeMillis() / 1000);
        
        conversionData.setDataDetail(dataDetail);
        
        // 测试发送
        String baseUrl = "https://convert.dop.360.cn";
        String appKey = "test_app_key";
        String appSecret = "test_app_secret";
        
        Boolean result = QihuSendConvertDataUtil.sendConvertData(baseUrl, appKey, appSecret, conversionData);
        log.info("移动推广转化数据回传结果: {}", result);
    }
}
