# 360点睛广告平台API对接指南

## 概述

本文档介绍如何使用360点睛广告平台的转化数据回传API。该API用于向360平台回传广告转化数据，支持多种广告类型的转化跟踪。

## 配置说明

### 1. 配置文件设置

在 `application-local.yml` 中添加360相关配置：

```yaml
nbchat-admin:
  config:
    qihu:
      app-key: your_360_app_key        # 360点睛平台App-Key
      app-secret: your_360_app_secret  # 360点睛平台App-Secret
      base-url: https://convert.dop.360.cn  # 360点睛平台基础URL
```

### 2. 获取360平台凭证

1. 登录360点睛广告平台
2. 在开发者中心获取App-Key和App-Secret
3. 配置转化跟踪权限

## API接口说明

### 接口地址
```
POST /admin/qihu/dataConversionCallback
```

### 请求参数

#### 请求体结构
```json
{
  "data": {
    "requestTime": 1640995200,
    "dataIndustry": "ocpc_ps_convert",
    "dataDetail": {
      "qhclickid": "click_id_123",
      "transId": "trans_123",
      "event": "SUBMIT",
      "eventTime": 1640995200
    }
  }
}
```

#### 参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| requestTime | Long | 是 | 请求时间戳 |
| dataIndustry | String | 是 | 数据行业类型 |
| dataDetail | Object | 是 | 数据详情 |

#### dataIndustry 取值说明

| 值 | 说明 |
|---|---|
| ocpc_ps_convert | PC搜索推广 |
| ocpc_ms_convert | 移动搜索 |
| ocpc_zs_convert | 展示广告 |
| ocpc_web_convert | 移动推广 |

#### dataDetail 字段说明

| 字段 | 类型 | 必填 | 适用场景 | 说明 |
|------|------|------|----------|------|
| qhclickid | String | 条件必填 | PC搜索、移动搜索、展示广告 | 360点击ID |
| impressionId | String | 条件必填 | 移动推广 | 展示ID |
| jzqs | String | 条件必填 | 展示广告 | 精准匹配参数 |
| transId | String | 是 | 所有场景 | 事务ID |
| event | String | 是 | 所有场景 | 事件类型(SUBMIT/ORDER) |
| eventTime | Long | 是 | 所有场景 | 事件时间戳 |

## 使用示例

### 1. PC搜索推广转化回传

```java
QihuApiCoordinationReqBO reqBo = new QihuApiCoordinationReqBO();
QihuConversionDataBO conversionData = new QihuConversionDataBO();
conversionData.setRequestTime(System.currentTimeMillis() / 1000);
conversionData.setDataIndustry("ocpc_ps_convert");

QihuDataDetailBO dataDetail = new QihuDataDetailBO();
dataDetail.setQhclickid("your_click_id");
dataDetail.setTransId("your_trans_id");
dataDetail.setEvent("SUBMIT");
dataDetail.setEventTime(System.currentTimeMillis() / 1000);

conversionData.setDataDetail(dataDetail);
reqBo.setData(conversionData);

// 调用接口
Rsp result = qihuApiCoordinationService.dataConversionCallback(reqBo);
```

### 2. 移动搜索转化回传

```java
QihuApiCoordinationReqBO reqBo = new QihuApiCoordinationReqBO();
QihuConversionDataBO conversionData = new QihuConversionDataBO();
conversionData.setRequestTime(System.currentTimeMillis() / 1000);
conversionData.setDataIndustry("ocpc_ms_convert");

QihuDataDetailBO dataDetail = new QihuDataDetailBO();
dataDetail.setQhclickid("your_click_id");
dataDetail.setTransId("your_trans_id");
dataDetail.setEvent("SUBMIT");
dataDetail.setEventTime(System.currentTimeMillis() / 1000);

conversionData.setDataDetail(dataDetail);
reqBo.setData(conversionData);

// 调用接口
Rsp result = qihuApiCoordinationService.dataConversionCallback(reqBo);
```

### 3. 展示广告转化回传

```java
QihuApiCoordinationReqBO reqBo = new QihuApiCoordinationReqBO();
QihuConversionDataBO conversionData = new QihuConversionDataBO();
conversionData.setRequestTime(System.currentTimeMillis() / 1000);
conversionData.setDataIndustry("ocpc_zs_convert");

QihuDataDetailBO dataDetail = new QihuDataDetailBO();
dataDetail.setQhclickid("your_click_id");
dataDetail.setJzqs("your_jzqs");
dataDetail.setTransId("your_trans_id");
dataDetail.setEvent("ORDER");
dataDetail.setEventTime(System.currentTimeMillis() / 1000);

conversionData.setDataDetail(dataDetail);
reqBo.setData(conversionData);

// 调用接口
Rsp result = qihuApiCoordinationService.dataConversionCallback(reqBo);
```

### 4. 移动推广转化回传

```java
QihuApiCoordinationReqBO reqBo = new QihuApiCoordinationReqBO();
QihuConversionDataBO conversionData = new QihuConversionDataBO();
conversionData.setRequestTime(System.currentTimeMillis() / 1000);
conversionData.setDataIndustry("ocpc_web_convert");

QihuDataDetailBO dataDetail = new QihuDataDetailBO();
dataDetail.setImpressionId("your_impression_id");
dataDetail.setTransId("your_trans_id");
dataDetail.setEvent("SUBMIT");
dataDetail.setEventTime(System.currentTimeMillis() / 1000);

conversionData.setDataDetail(dataDetail);
reqBo.setData(conversionData);

// 调用接口
Rsp result = qihuApiCoordinationService.dataConversionCallback(reqBo);
```

## 响应说明

### 成功响应
```json
{
  "code": 200,
  "message": "360回传数据成功",
  "data": null
}
```

### 失败响应
```json
{
  "code": 500,
  "message": "360回传数据失败",
  "data": null
}
```

## 注意事项

1. **签名机制**: 使用MD5签名，签名字符串为 `app_secret + request_body`
2. **重试机制**: 内置3次重试机制，遇到服务端错误会自动重试
3. **参数校验**: 请确保必填参数完整，不同广告类型需要的参数不同
4. **时间戳**: 所有时间戳使用Unix时间戳（秒级）
5. **编码格式**: 请求和响应均使用UTF-8编码

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| 400 | 参数错误 | 检查请求参数格式和必填字段 |
| 401 | 认证失败 | 检查App-Key和签名是否正确 |
| 500 | 服务端错误 | 稍后重试或联系技术支持 |

## 测试

运行测试用例验证接口功能：

```bash
mvn test -Dtest=QihuApiTest
```

## 技术支持

如有问题，请联系360点睛平台技术支持或查阅官方文档。
